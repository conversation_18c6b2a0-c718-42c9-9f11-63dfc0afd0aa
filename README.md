# Chat 1-1 App - Nuxt 3 + Supabase + Tailwind CSS

Ứng dụng chat 1-1 realtime được xây dựng với Nuxt 3, Supabase và Tailwind CSS.

## Tính năng

- ✅ **Đăng nhập/Đăng ký** bằng Supabase Auth (email/password)
- ✅ **Danh sách users** - Hiển thị tất cả users để chọn chat
- ✅ **Chat 1-1** - Mỗi cặp user có conversation riêng biệt
- ✅ **Realtime messaging** - Tin nhắn cập nhật realtime với Supabase
- ✅ **Auto profile creation** - Tự động tạo profile khi đăng ký
- ✅ **UI responsive** với Nuxt UI + Tailwind CSS
- ✅ **Search users** - Tìm kiếm users theo tên/email
- ✅ **Avatar system** - Avatar tự động từ initials
- ✅ **Row Level Security (RLS)** cho bảo mật
- ✅ **Debug panel** - Debug realtime trong development mode

## Cấu trúc dự án

```
├── components/
│   ├── UserList.vue         # Danh sách users (sidebar)
│   ├── ChatBox.vue          # Component chat chính
│   ├── LoginForm.vue        # Form đăng nhập/đăng ký
│   ├── MessageList.vue      # Danh sách tin nhắn
│   ├── MessageInput.vue     # Input gửi tin nhắn
│   └── RealtimeDebug.vue    # Debug panel (dev only)
├── middleware/
│   └── auth.ts              # Middleware bảo vệ route
├── pages/
│   ├── index.vue            # Trang chủ (redirect)
│   ├── login.vue            # Trang đăng nhập
│   └── chat.vue             # Trang chat chính
├── supabase/
│   └── migrations/          # Database migrations
└── setup files/
    ├── enable-realtime.sql  # Script setup database
    ├── SETUP_1_1_CHAT.md    # Hướng dẫn setup chi tiết
    └── test-connection.html # Test kết nối Supabase
```

## Cài đặt

1. **Clone repository và cài đặt dependencies:**

```bash
npm install
```

2. **Cấu hình Supabase:**

Tạo file `.env` từ `.env.example`:

```bash
cp .env.example .env
```

Cập nhật các biến môi trường trong `.env`:

```env
SUPABASE_URL=your_supabase_project_url
SUPABASE_KEY=your_supabase_anon_key
```

3. **Setup Database cho Chat 1-1:**

**⚠️ QUAN TRỌNG: Sử dụng script mới cho hệ thống chat 1-1**

1. Mở [Supabase Dashboard](https://supabase.com/dashboard)
2. Vào **SQL Editor**
3. Copy toàn bộ nội dung file `enable-realtime.sql`
4. Paste và nhấn **Run**

Script sẽ tự động:

- Tạo bảng `messages` mới với cấu trúc 1-1 chat
- Tạo bảng `profiles` cho thông tin users
- Setup RLS policies phù hợp
- Tạo trigger tự động tạo profile khi đăng ký
- Enable realtime cho cả 2 bảng

**Đọc hướng dẫn chi tiết:** `SETUP_1_1_CHAT.md`

4. **Khởi động development server:**

```bash
npm run dev
```

Ứng dụng sẽ chạy tại `http://localhost:3000`

## Sử dụng

1. **Truy cập ứng dụng:** `http://localhost:3000`
2. **Đăng ký/Đăng nhập:** Tạo tài khoản mới hoặc đăng nhập
3. **Chọn user:** Từ danh sách bên trái, click vào user muốn chat
4. **Bắt đầu chat:** Gửi tin nhắn và xem realtime updates!

### Demo với 2 users:

- Mở 2 tab browser
- Đăng ký 2 tài khoản khác nhau
- Tab 1: Chọn user 2 và gửi tin nhắn
- Tab 2: Chọn user 1 và xem tin nhắn hiển thị realtime

## Troubleshooting

### Realtime không hoạt động?

1. **Kiểm tra Database Setup:**

   - Chạy file `enable-realtime.sql` trong Supabase SQL Editor
   - Đảm bảo bảng `messages` có trong realtime publication

2. **Kiểm tra Debug Panel:**

   - Trong development mode, sẽ có debug panel ở đầu trang chat
   - Connection Status phải là "SUBSCRIBED"
   - Thử nhấn "Test Connection" và "Send Test Message"

3. **Kiểm tra Console Logs:**

   - Mở F12 > Console
   - Tìm log "✅ Successfully subscribed to realtime messages"

4. **Xem hướng dẫn chi tiết:**
   - Đọc file `REALTIME_TROUBLESHOOTING.md` để troubleshoot chi tiết

## Cấu trúc Database

### Bảng `messages` (Chat 1-1)

- `id`: UUID (Primary Key)
- `sender_id`: UUID (Foreign Key đến auth.users)
- `receiver_id`: UUID (Foreign Key đến auth.users)
- `message`: TEXT (Nội dung tin nhắn)
- `created_at`: TIMESTAMP

### Bảng `profiles` (Thông tin users)

- `id`: UUID (Primary Key, Foreign Key đến auth.users)
- `full_name`: TEXT (Tên đầy đủ)
- `email`: TEXT (Email)
- `avatar_url`: TEXT (URL avatar)
- `created_at`: TIMESTAMP
- `updated_at`: TIMESTAMP

### Row Level Security (RLS)

**Messages:**

- Users chỉ xem được tin nhắn mà họ là sender hoặc receiver
- Users chỉ có thể gửi tin nhắn với sender_id của chính mình

**Profiles:**

- Tất cả users đã xác thực có thể xem profiles (để hiển thị danh sách)
- Users chỉ có thể insert/update profile của chính mình

## Công nghệ sử dụng

- **Nuxt 3**: Vue.js framework
- **Supabase**: Backend-as-a-Service (Database + Auth + Realtime)
- **Nuxt UI**: Component library với Tailwind CSS
- **TypeScript**: Type safety
- **date-fns**: Date formatting

## Production

Build ứng dụng cho production:

```bash
npm run build
```

Preview production build:

```bash
npm run preview
```

## Deployment

Ứng dụng có thể deploy lên:

- Vercel
- Netlify
- Supabase (Static hosting)
- Hoặc bất kỳ platform nào hỗ trợ Nuxt 3

Đảm bảo cấu hình đúng các biến môi trường trên platform deployment.
