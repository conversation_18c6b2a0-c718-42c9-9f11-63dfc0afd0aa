# Chat Realtime App - Nuxt 3 + Supabase + Tailwind CSS

Ứng dụng chat realtime đơn giản được xây dựng với Nuxt 3, Supabase và Tailwind CSS.

## Tính năng

- ✅ Đăng nhập/Đăng ký bằng Supabase Auth (email/password)
- ✅ Chat realtime với Supabase Realtime
- ✅ UI đẹp với Nuxt UI + Tailwind CSS
- ✅ Responsive design
- ✅ Tự động scroll xuống tin nhắn mới
- ✅ Hiển thị thời gian tin nhắn
- ✅ Phân biệt tin nhắn của bản thân và người khác
- ✅ Row Level Security (RLS) cho bảo mật

## Cấu trúc dự án

```
├── components/
│   ├── ChatBox.vue          # Component chat chính
│   ├── LoginForm.vue        # Form đăng nhập/đăng ký
│   ├── MessageList.vue      # Danh sách tin nhắn
│   └── MessageInput.vue     # Input gửi tin nhắn
├── middleware/
│   └── auth.ts              # Middleware bảo vệ route
├── pages/
│   ├── index.vue            # Trang chủ (redirect)
│   ├── login.vue            # Trang đăng nhập
│   └── chat.vue             # Trang chat chính
├── supabase/
│   └── migrations/          # Database migrations
└── types/
    └── supabase.ts          # TypeScript types
```

## Cài đặt

1. **Clone repository và cài đặt dependencies:**

```bash
npm install
```

2. **Cấu hình Supabase:**

Tạo file `.env` từ `.env.example`:

```bash
cp .env.example .env
```

Cập nhật các biến môi trường trong `.env`:

```env
SUPABASE_URL=your_supabase_project_url
SUPABASE_KEY=your_supabase_anon_key
```

3. **Chạy migrations:**

Nếu bạn sử dụng Supabase CLI:

```bash
supabase db push
```

Hoặc chạy SQL trong Supabase Dashboard từ file `supabase/migrations/20240320000000_initial_schema.sql`

4. **Khởi động development server:**

```bash
npm run dev
```

Ứng dụng sẽ chạy tại `http://localhost:3000`

## Sử dụng

1. Truy cập `http://localhost:3000`
2. Đăng ký tài khoản mới hoặc đăng nhập
3. Bắt đầu chat realtime!

## Cấu trúc Database

### Bảng `messages`

- `id`: UUID (Primary Key)
- `user_id`: UUID (Foreign Key đến auth.users)
- `content`: TEXT (Nội dung tin nhắn)
- `created_at`: TIMESTAMP

### Row Level Security (RLS)

- Người dùng đã xác thực có thể xem tất cả tin nhắn
- Chỉ có thể gửi tin nhắn với user_id của chính mình

## Công nghệ sử dụng

- **Nuxt 3**: Vue.js framework
- **Supabase**: Backend-as-a-Service (Database + Auth + Realtime)
- **Nuxt UI**: Component library với Tailwind CSS
- **TypeScript**: Type safety
- **date-fns**: Date formatting

## Production

Build ứng dụng cho production:

```bash
npm run build
```

Preview production build:

```bash
npm run preview
```

## Deployment

Ứng dụng có thể deploy lên:

- Vercel
- Netlify
- Supabase (Static hosting)
- Hoặc bất kỳ platform nào hỗ trợ Nuxt 3

Đảm bảo cấu hình đúng các biến môi trường trên platform deployment.
