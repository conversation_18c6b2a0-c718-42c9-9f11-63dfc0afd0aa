<template>
  <div class="bg-white border-r border-gray-200 w-80 flex flex-col">
    <!-- Header -->
    <div class="p-4 border-b border-gray-200">
      <h2 class="text-lg font-semibold text-gray-900">Danh sách người dùng</h2>
      <p class="text-sm text-gray-500">{{ users.length }} người dùng</p>
    </div>

    <!-- Search -->
    <div class="p-4 border-b border-gray-200">
      <UInput
        v-model="searchQuery"
        placeholder="Tìm kiếm người dùng..."
        icon="heroicons:magnifying-glass"
      />
    </div>

    <!-- Users List -->
    <div class="flex-1 overflow-y-auto">
      <!-- Loading -->
      <div v-if="loading" class="p-4 text-center">
        <Icon name="heroicons:arrow-path" class="w-6 h-6 animate-spin mx-auto text-gray-400" />
        <p class="text-sm text-gray-500 mt-2"><PERSON><PERSON> tải...</p>
      </div>

      <!-- Empty state -->
      <div v-else-if="filteredUsers.length === 0" class="p-4 text-center">
        <Icon name="heroicons:users" class="w-12 h-12 mx-auto text-gray-300 mb-2" />
        <p class="text-gray-500">Không tìm thấy người dùng nào</p>
      </div>

      <!-- Users -->
      <div v-else class="divide-y divide-gray-100">
        <div
          v-for="user in filteredUsers"
          :key="user.id"
          @click="selectUser(user)"
          :class="[
            'p-4 hover:bg-gray-50 cursor-pointer transition-colors',
            selectedUserId === user.id ? 'bg-blue-50 border-r-2 border-blue-500' : ''
          ]"
        >
          <div class="flex items-center space-x-3">
            <!-- Avatar -->
            <div class="flex-shrink-0">
              <div
                v-if="user.avatar_url"
                class="w-10 h-10 rounded-full bg-cover bg-center"
                :style="{ backgroundImage: `url(${user.avatar_url})` }"
              ></div>
              <div
                v-else
                class="w-10 h-10 rounded-full bg-blue-500 flex items-center justify-center text-white font-medium"
              >
                {{ getInitials(user.full_name || user.email) }}
              </div>
            </div>

            <!-- User Info -->
            <div class="flex-1 min-w-0">
              <p class="text-sm font-medium text-gray-900 truncate">
                {{ user.full_name || user.email?.split('@')[0] || 'Ẩn danh' }}
              </p>
              <p class="text-sm text-gray-500 truncate">
                {{ user.email }}
              </p>
              <!-- Last message preview (if available) -->
              <p v-if="user.lastMessage" class="text-xs text-gray-400 truncate mt-1">
                {{ user.lastMessage }}
              </p>
            </div>

            <!-- Online status (mock) -->
            <div class="flex-shrink-0">
              <div class="w-3 h-3 bg-green-400 rounded-full"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface User {
  id: string
  email: string
  full_name?: string
  avatar_url?: string
  created_at: string
  lastMessage?: string
}

interface Emits {
  (e: 'user-selected', user: User): void
}

const emit = defineEmits<Emits>()

const supabase = useSupabaseClient()
const currentUser = useSupabaseUser()
const toast = useToast()

const users = ref<User[]>([])
const loading = ref(true)
const searchQuery = ref('')
const selectedUserId = ref<string>('')

// Computed filtered users
const filteredUsers = computed(() => {
  if (!searchQuery.value) return users.value
  
  const query = searchQuery.value.toLowerCase()
  return users.value.filter(user => 
    user.full_name?.toLowerCase().includes(query) ||
    user.email?.toLowerCase().includes(query)
  )
})

// Get user initials for avatar
const getInitials = (name: string) => {
  if (!name) return '?'
  return name
    .split(' ')
    .map(word => word[0])
    .join('')
    .toUpperCase()
    .slice(0, 2)
}

// Select user
const selectUser = (user: User) => {
  selectedUserId.value = user.id
  emit('user-selected', user)
}

// Fetch users
const fetchUsers = async () => {
  try {
    loading.value = true
    
    const { data, error } = await supabase
      .from('profiles')
      .select('*')
      .neq('id', currentUser.value?.id) // Exclude current user
      .order('created_at', { ascending: false })

    if (error) throw error

    users.value = data || []
  } catch (error) {
    console.error('Error fetching users:', error)
    toast.add({
      title: 'Lỗi tải danh sách',
      description: 'Không thể tải danh sách người dùng',
      color: 'red'
    })
  } finally {
    loading.value = false
  }
}

// Lifecycle
onMounted(() => {
  fetchUsers()
})

// Expose methods for parent component
defineExpose({
  fetchUsers,
  selectUser: (userId: string) => {
    const user = users.value.find(u => u.id === userId)
    if (user) selectUser(user)
  }
})
</script>
