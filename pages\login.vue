<template>
  <div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
      <div>
        <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
          Đ<PERSON><PERSON> nhập v<PERSON><PERSON>
        </h2>
        <p class="mt-2 text-center text-sm text-gray-600">
          Hoặc
          <button @click="isRegister = !isRegister" class="font-medium text-primary-600 hover:text-primary-500">
            {{ isRegister ? 'đăng nhập' : 'đăng ký tài khoản mới' }}
          </button>
        </p>
      </div>
      
      <LoginForm :is-register="isRegister" @toggle-mode="isRegister = !isRegister" />
    </div>
  </div>
</template>

<script setup lang="ts">
const user = useSupabaseUser()
const isRegister = ref(false)

// Redirect if already logged in
watch(user, (newUser) => {
  if (newUser) {
    navigateTo('/chat')
  }
}, { immediate: true })

definePageMeta({
  layout: false
})
</script>
