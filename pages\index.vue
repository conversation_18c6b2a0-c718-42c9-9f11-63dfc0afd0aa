<template>
  <div class="min-h-screen flex items-center justify-center bg-gray-50">
    <div class="text-center">
      <Icon name="heroicons:arrow-path" class="w-8 h-8 text-gray-400 animate-spin mx-auto mb-4" />
      <p class="text-gray-600"><PERSON><PERSON> h<PERSON>ớng...</p>
    </div>
  </div>
</template>

<script setup lang="ts">
const user = useSupabaseUser()

// Redirect based on auth status
watch(user, (newUser) => {
  if (newUser) {
    navigateTo('/chat')
  } else {
    navigateTo('/login')
  }
}, { immediate: true })

definePageMeta({
  layout: false
})
</script>
