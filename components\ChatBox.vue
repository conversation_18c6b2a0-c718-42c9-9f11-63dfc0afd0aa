<template>
  <div class="flex flex-col h-full">
    <!-- Messages List -->
    <div class="flex-1 overflow-hidden">
      <MessageList :messages="messages" :loading="loading" />
    </div>

    <!-- Message Input -->
    <div class="border-t border-gray-200 bg-white p-4">
      <MessageInput @send-message="sendMessage" :sending="sending" />
    </div>
  </div>
</template>

<script setup lang="ts">
import type { RealtimeChannel } from "@supabase/supabase-js";

interface Message {
  id: string;
  user_id: string;
  content: string;
  created_at: string;
}

interface Emits {
  (e: "messages-updated", count: number): void;
}

const emit = defineEmits<Emits>();

const supabase = useSupabaseClient();
const user = useSupabaseUser();
const toast = useToast();

const messages = ref<Message[]>([]);
const loading = ref(true);
const sending = ref(false);

let realtimeChannel: RealtimeChannel;

// Fetch initial messages
const fetchMessages = async () => {
  try {
    const { data, error } = await supabase
      .from("messages")
      .select(
        `
        id,
        user_id,
        content,
        created_at
      `
      )
      .order("created_at", { ascending: true })
      .limit(50);

    if (error) throw error;

    messages.value = data || [];
    emit("messages-updated", messages.value.length);
  } catch (error) {
    console.error("Error fetching messages:", error);
    toast.add({
      title: "Lỗi tải tin nhắn",
      description: "Không thể tải tin nhắn",
      color: "red",
    });
  } finally {
    loading.value = false;
  }
};

// Send message
const sendMessage = async (content: string) => {
  if (!content.trim() || !user.value) {
    console.log("Cannot send message: content or user missing", {
      content,
      user: user.value,
    });
    return;
  }

  sending.value = true;
  console.log("Sending message:", { content, userId: user.value.id });

  try {
    const { data, error } = await supabase
      .from("messages")
      .insert({
        user_id: user.value.id,
        content: content.trim(),
      })
      .select();

    console.log("Insert result:", { data, error });

    if (error) throw error;

    toast.add({
      title: "Tin nhắn đã gửi",
      color: "green",
    });
  } catch (error) {
    console.error("Error sending message:", error);
    toast.add({
      title: "Lỗi gửi tin nhắn",
      description: error.message || "Không thể gửi tin nhắn",
      color: "red",
    });
  } finally {
    sending.value = false;
  }
};

// Setup realtime subscription
const setupRealtimeSubscription = () => {
  console.log("Setting up realtime subscription...");

  realtimeChannel = supabase
    .channel("public:messages", {
      config: {
        broadcast: { self: true },
      },
    })
    .on(
      "postgres_changes",
      {
        event: "INSERT",
        schema: "public",
        table: "messages",
      },
      (payload) => {
        console.log("Realtime message received:", payload);

        // Add the new message directly from payload
        const newMessage = {
          id: payload.new.id,
          user_id: payload.new.user_id,
          content: payload.new.content,
          created_at: payload.new.created_at,
        };

        console.log("Adding new message to list:", newMessage);
        messages.value.push(newMessage);
        emit("messages-updated", messages.value.length);
      }
    )
    .subscribe((status) => {
      console.log("Realtime subscription status:", status);

      if (status === "SUBSCRIBED") {
        console.log("✅ Successfully subscribed to realtime messages");
      } else if (status === "CHANNEL_ERROR") {
        console.error("❌ Realtime subscription error");
        toast.add({
          title: "Lỗi Realtime",
          description: "Không thể kết nối realtime",
          color: "red",
        });
      }
    });
};

// Lifecycle
onMounted(async () => {
  await fetchMessages();
  setupRealtimeSubscription();
});

onUnmounted(() => {
  if (realtimeChannel) {
    supabase.removeChannel(realtimeChannel);
  }
});
</script>
