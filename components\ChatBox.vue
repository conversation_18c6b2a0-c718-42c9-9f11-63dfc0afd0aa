<template>
  <div class="flex flex-col h-full">
    <!-- Cha<PERSON> -->
    <div v-if="selectedUser" class="bg-white border-b border-gray-200 p-4">
      <div class="flex items-center space-x-3">
        <!-- Avatar -->
        <div class="flex-shrink-0">
          <div
            v-if="selectedUser.avatar_url"
            class="w-10 h-10 rounded-full bg-cover bg-center"
            :style="{ backgroundImage: `url(${selectedUser.avatar_url})` }"
          ></div>
          <div
            v-else
            class="w-10 h-10 rounded-full bg-blue-500 flex items-center justify-center text-white font-medium"
          >
            {{ getInitials(selectedUser.full_name || selectedUser.email) }}
          </div>
        </div>

        <!-- User Info -->
        <div class="flex-1">
          <h3 class="text-lg font-semibold text-gray-900">
            {{
              selectedUser.full_name ||
              selectedUser.email?.split("@")[0] ||
              "Ẩn danh"
            }}
          </h3>
          <p class="text-sm text-gray-500">{{ selectedUser.email }}</p>
        </div>

        <!-- Online status -->
        <div class="flex items-center space-x-2">
          <div class="w-3 h-3 bg-green-400 rounded-full"></div>
          <span class="text-sm text-gray-500">Đang hoạt động</span>
        </div>
      </div>
    </div>

    <!-- No user selected -->
    <div
      v-if="!selectedUser"
      class="flex-1 flex items-center justify-center bg-gray-50"
    >
      <div class="text-center">
        <Icon
          name="heroicons:chat-bubble-left-right"
          class="w-16 h-16 text-gray-300 mx-auto mb-4"
        />
        <h3 class="text-lg font-medium text-gray-900 mb-2">
          Chọn một người để bắt đầu chat
        </h3>
        <p class="text-gray-500">
          Chọn người dùng từ danh sách bên trái để bắt đầu cuộc trò chuyện
        </p>
      </div>
    </div>

    <!-- Chat content when user is selected -->
    <div v-else class="flex-1 flex flex-col overflow-hidden">
      <!-- Messages List -->
      <div class="flex-1 overflow-hidden">
        <MessageList
          :messages="messages"
          :loading="loading"
          :current-user-id="currentUser?.id"
        />
      </div>

      <!-- Message Input -->
      <div class="border-t border-gray-200 bg-white p-4">
        <MessageInput @send-message="sendMessage" :sending="sending" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { RealtimeChannel } from "@supabase/supabase-js";

interface Message {
  id: string;
  sender_id: string;
  receiver_id: string;
  message: string;
  created_at: string;
}

interface User {
  id: string;
  email: string;
  full_name?: string;
  avatar_url?: string;
}

interface Props {
  selectedUser?: User;
}

interface Emits {
  (e: "messages-updated", count: number): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const supabase = useSupabaseClient();
const currentUser = useSupabaseUser();
const toast = useToast();

const messages = ref<Message[]>([]);
const loading = ref(true);
const sending = ref(false);

let realtimeChannel: RealtimeChannel;

// Computed
const selectedUser = computed(() => props.selectedUser);

// Get user initials for avatar
const getInitials = (name: string) => {
  if (!name) return "?";
  return name
    .split(" ")
    .map((word) => word[0])
    .join("")
    .toUpperCase()
    .slice(0, 2);
};

// Fetch initial messages
const fetchMessages = async () => {
  if (!selectedUser.value || !currentUser.value) {
    messages.value = [];
    loading.value = false;
    return;
  }

  try {
    loading.value = true;

    const { data, error } = await supabase
      .from("messages")
      .select("*")
      .or(
        `and(sender_id.eq.${currentUser.value.id},receiver_id.eq.${selectedUser.value.id}),and(sender_id.eq.${selectedUser.value.id},receiver_id.eq.${currentUser.value.id})`
      )
      .order("created_at", { ascending: true })
      .limit(50);

    if (error) throw error;

    messages.value = data || [];
    emit("messages-updated", messages.value.length);
  } catch (error) {
    console.error("Error fetching messages:", error);
    toast.add({
      title: "Lỗi tải tin nhắn",
      description: "Không thể tải tin nhắn",
      color: "red",
    });
  } finally {
    loading.value = false;
  }
};

// Send message
const sendMessage = async (content: string) => {
  if (!content.trim() || !currentUser.value || !selectedUser.value) {
    console.log("Cannot send message: missing data", {
      content,
      currentUser: currentUser.value,
      selectedUser: selectedUser.value,
    });
    return;
  }

  sending.value = true;
  console.log("Sending message:", {
    content,
    senderId: currentUser.value.id,
    receiverId: selectedUser.value.id,
  });

  try {
    const { data, error } = await supabase
      .from("messages")
      .insert({
        sender_id: currentUser.value.id,
        receiver_id: selectedUser.value.id,
        message: content.trim(),
      })
      .select();

    console.log("Insert result:", { data, error });

    if (error) throw error;

    toast.add({
      title: "Tin nhắn đã gửi",
      color: "green",
    });
  } catch (error: any) {
    console.error("Error sending message:", error);
    toast.add({
      title: "Lỗi gửi tin nhắn",
      description: error.message || "Không thể gửi tin nhắn",
      color: "red",
    });
  } finally {
    sending.value = false;
  }
};

// Setup realtime subscription
const setupRealtimeSubscription = () => {
  if (!currentUser.value) return;

  console.log("Setting up realtime subscription...");

  realtimeChannel = supabase
    .channel("messages", {
      config: {
        broadcast: { self: true },
      },
    })
    .on(
      "postgres_changes",
      {
        event: "INSERT",
        schema: "public",
        table: "messages",
      },
      (payload) => {
        console.log("Realtime message received:", payload);

        // Only add message if it's relevant to current conversation
        const newMessage = payload.new;
        const isRelevant =
          (newMessage.sender_id === currentUser.value?.id &&
            newMessage.receiver_id === selectedUser.value?.id) ||
          (newMessage.sender_id === selectedUser.value?.id &&
            newMessage.receiver_id === currentUser.value?.id);

        if (isRelevant) {
          const message = {
            id: newMessage.id,
            sender_id: newMessage.sender_id,
            receiver_id: newMessage.receiver_id,
            message: newMessage.message,
            created_at: newMessage.created_at,
          };

          console.log("Adding new message to list:", message);
          messages.value.push(message);
          emit("messages-updated", messages.value.length);
        }
      }
    )
    .subscribe((status) => {
      console.log("Realtime subscription status:", status);

      if (status === "SUBSCRIBED") {
        console.log("✅ Successfully subscribed to realtime messages");
      } else if (status === "CHANNEL_ERROR") {
        console.error("❌ Realtime subscription error");
        toast.add({
          title: "Lỗi Realtime",
          description: "Không thể kết nối realtime",
          color: "red",
        });
      }
    });
};

// Watch selectedUser changes
watch(
  selectedUser,
  async (newUser) => {
    if (newUser) {
      await fetchMessages();
    } else {
      messages.value = [];
    }
  },
  { immediate: true }
);

// Lifecycle
onMounted(() => {
  setupRealtimeSubscription();
});

onUnmounted(() => {
  if (realtimeChannel) {
    supabase.removeChannel(realtimeChannel);
  }
});
</script>
