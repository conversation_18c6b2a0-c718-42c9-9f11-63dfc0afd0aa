<template>
  <div class="h-screen flex flex-col bg-gray-50">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b border-gray-200 px-4 py-3">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-3">
          <div
            class="w-8 h-8 bg-primary-500 rounded-full flex items-center justify-center"
          >
            <Icon
              name="heroicons:chat-bubble-left-right"
              class="w-5 h-5 text-white"
            />
          </div>
          <div>
            <h1 class="text-lg font-semibold text-gray-900">Chat Realtime</h1>
            <p class="text-sm text-gray-500">
              {{ onlineUsers }} người đang online
            </p>
          </div>
        </div>

        <div class="flex items-center space-x-3">
          <div class="text-sm text-gray-600">Xin chào, {{ user?.email }}</div>
          <UButton @click="handleLogout" variant="ghost" color="red" size="sm">
            Đăng xuất
          </UButton>
        </div>
      </div>
    </header>

    <!-- Debug Panel (only in development) -->
    <div v-if="isDev" class="border-b border-gray-200">
      <RealtimeDebug :messages-count="messagesCount" />
    </div>

    <!-- Chat Container -->
    <div class="flex-1 flex flex-col overflow-hidden">
      <ChatBox @messages-updated="updateMessagesCount" />
    </div>
  </div>
</template>

<script setup lang="ts">
definePageMeta({
  middleware: "auth",
  layout: false,
});

const user = useSupabaseUser();
const supabase = useSupabaseClient();
const toast = useToast();

// Mock online users count - in real app, you'd track this via presence
const onlineUsers = ref(1);

// Debug mode (only show in development)
const isDev = process.env.NODE_ENV === "development";

// Messages count for debug
const messagesCount = ref(0);

const updateMessagesCount = (count: number) => {
  messagesCount.value = count;
};

const handleLogout = async () => {
  try {
    await supabase.auth.signOut();
    toast.add({
      title: "Đã đăng xuất",
      color: "green",
    });
    await navigateTo("/login");
  } catch (error) {
    toast.add({
      title: "Lỗi đăng xuất",
      description: "Có lỗi xảy ra khi đăng xuất",
      color: "red",
    });
  }
};
</script>
