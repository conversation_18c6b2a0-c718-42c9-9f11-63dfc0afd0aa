export const useProfile = () => {
  const supabase = useSupabaseClient()
  const user = useSupabaseUser()

  const createProfile = async (fullName?: string) => {
    if (!user.value) return

    try {
      const { error } = await supabase
        .from('profiles')
        .upsert({
          id: user.value.id,
          full_name: fullName || user.value.email?.split('@')[0] || 'Ẩn danh',
          updated_at: new Date().toISOString()
        })

      if (error) throw error
    } catch (error) {
      console.error('Error creating profile:', error)
    }
  }

  const getProfile = async (userId: string) => {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single()

      if (error) throw error
      return data
    } catch (error) {
      console.error('Error fetching profile:', error)
      return null
    }
  }

  return {
    createProfile,
    getProfile
  }
}
