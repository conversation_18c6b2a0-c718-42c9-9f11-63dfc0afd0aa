-- =============================================
-- ENABLE REALTIME FOR MESSAGES TABLE
-- =============================================

-- Check if realtime is enabled for messages table
SELECT 
    schemaname,
    tablename,
    pubname
FROM pg_publication_tables 
WHERE tablename = 'messages';

-- If not found, add messages table to realtime publication
-- Note: This might fail if table is already added, that's OK
DO $$
BEGIN
    -- Try to add table to publication
    BEGIN
        ALTER PUBLICATION supabase_realtime ADD TABLE messages;
        RAISE NOTICE 'Added messages table to realtime publication';
    EXCEPTION 
        WHEN duplicate_object THEN
            RAISE NOTICE 'Messages table already in realtime publication';
        WHEN OTHERS THEN
            RAISE NOTICE 'Error adding table to publication: %', SQLERRM;
    END;
END $$;

-- Verify realtime is enabled
SELECT 
    schemaname,
    tablename,
    pubname
FROM pg_publication_tables 
WHERE tablename = 'messages';

-- Check if RLS policies exist
SELECT 
    schemaname,
    tablename,
    policyname,
    cmd
FROM pg_policies 
WHERE tablename = 'messages';

-- Test query to ensure table is accessible
SELECT 
    COUNT(*) as message_count,
    'Messages table is accessible' as status
FROM messages;

-- Success message
SELECT 'Realtime setup completed! 🚀' AS result;
