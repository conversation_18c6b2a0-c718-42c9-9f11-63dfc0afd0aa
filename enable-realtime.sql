-- =============================================
-- UPGRADE TO 1-1 CHAT SYSTEM
-- =============================================

-- Drop old messages table if exists
DROP TABLE IF EXISTS messages CASCADE;

-- Create new messages table for 1-1 chat
CREATE TABLE messages (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    sender_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    receiver_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    message TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL
);

-- Create profiles table if not exists
CREATE TABLE IF NOT EXISTS profiles (
    id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
    full_name TEXT,
    email TEXT,
    avatar_url TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL
);

-- Create indexes for better performance
CREATE INDEX messages_sender_id_idx ON messages(sender_id);
CREATE INDEX messages_receiver_id_idx ON messages(receiver_id);
CREATE INDEX messages_created_at_idx ON messages(created_at);
CREATE INDEX messages_conversation_idx ON messages(sender_id, receiver_id);

-- Enable Row Level Security
ALTER TABLE messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;

-- RLS Policies for messages
-- Users can only see messages where they are sender or receiver
CREATE POLICY "Users can view their own messages"
    ON messages FOR SELECT
    TO authenticated
    USING (auth.uid() = sender_id OR auth.uid() = receiver_id);

-- Users can only insert messages as sender
CREATE POLICY "Users can insert messages as sender"
    ON messages FOR INSERT
    TO authenticated
    WITH CHECK (auth.uid() = sender_id);

-- RLS Policies for profiles
-- Anyone authenticated can view all profiles (for user list)
CREATE POLICY "Anyone can view profiles"
    ON profiles FOR SELECT
    TO authenticated
    USING (true);

-- Users can only insert/update their own profile
CREATE POLICY "Users can insert their own profile"
    ON profiles FOR INSERT
    TO authenticated
    WITH CHECK (auth.uid() = id);

CREATE POLICY "Users can update their own profile"
    ON profiles FOR UPDATE
    TO authenticated
    USING (auth.uid() = id);

-- Create function to automatically create profile when user signs up
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.profiles (id, full_name, email)
    VALUES (
        NEW.id,
        COALESCE(NEW.raw_user_meta_data->>'full_name', split_part(NEW.email, '@', 1)),
        NEW.email
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger to call the function when a new user is created
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Enable realtime for both tables
DO $$
BEGIN
    -- Try to add tables to publication
    BEGIN
        ALTER PUBLICATION supabase_realtime ADD TABLE messages;
        RAISE NOTICE 'Added messages table to realtime publication';
    EXCEPTION
        WHEN duplicate_object THEN
            RAISE NOTICE 'Messages table already in realtime publication';
        WHEN OTHERS THEN
            RAISE NOTICE 'Error adding messages table: %', SQLERRM;
    END;

    BEGIN
        ALTER PUBLICATION supabase_realtime ADD TABLE profiles;
        RAISE NOTICE 'Added profiles table to realtime publication';
    EXCEPTION
        WHEN duplicate_object THEN
            RAISE NOTICE 'Profiles table already in realtime publication';
        WHEN OTHERS THEN
            RAISE NOTICE 'Error adding profiles table: %', SQLERRM;
    END;
END $$;

-- Verify realtime is enabled
SELECT
    schemaname,
    tablename,
    pubname
FROM pg_publication_tables
WHERE tablename = 'messages';

-- Check if RLS policies exist
SELECT
    schemaname,
    tablename,
    policyname,
    cmd
FROM pg_policies
WHERE tablename = 'messages';

-- Test query to ensure table is accessible
SELECT
    COUNT(*) as message_count,
    'Messages table is accessible' as status
FROM messages;

-- Success message
SELECT 'Realtime setup completed! 🚀' AS result;
