# 🚀 Setup <PERSON>ệ thống Chat 1-1

## Tổng quan

Ứng dụng đã được nâng cấp thành hệ thống chat 1-1 với các tính năng:

- ✅ Danh sách tất cả users (ngoại trừ chính mình)
- ✅ Chat 1-1 riêng biệt giữa từng cặp users
- ✅ Realtime messaging với Supabase
- ✅ UI responsive với Tailwind CSS
- ✅ Tự động tạo profile khi đăng ký

## Cấu trúc Database mới

### Bảng `messages`
```sql
messages (
    id UUID PRIMARY KEY,
    sender_id UUID REFERENCES auth.users(id),
    receiver_id UUID REFERENCES auth.users(id), 
    message TEXT,
    created_at TIMESTAMP
)
```

### Bảng `profiles`
```sql
profiles (
    id UUID PRIMARY KEY REFERENCES auth.users(id),
    full_name TEXT,
    email TEXT,
    avatar_url TEXT,
    created_at TIMESTAMP,
    updated_at TIMESTAMP
)
```

## Bước 1: Cập nhật Database

**Chạy script nâng cấp:**

1. Mở [Supabase Dashboard](https://supabase.com/dashboard)
2. Vào **SQL Editor**
3. Copy toàn bộ nội dung file `enable-realtime.sql`
4. Paste và nhấn **Run**

Script sẽ:
- Xóa bảng messages cũ và tạo lại với cấu trúc mới
- Tạo bảng profiles nếu chưa có
- Tạo RLS policies phù hợp
- Tạo trigger tự động tạo profile khi user đăng ký
- Enable realtime cho cả 2 bảng

## Bước 2: Kiểm tra Setup

**Kiểm tra bảng đã tạo:**
```sql
-- Kiểm tra cấu trúc bảng messages
\d messages;

-- Kiểm tra cấu trúc bảng profiles  
\d profiles;

-- Kiểm tra RLS policies
SELECT tablename, policyname, cmd 
FROM pg_policies 
WHERE tablename IN ('messages', 'profiles');
```

**Kiểm tra realtime:**
```sql
-- Kiểm tra bảng có trong realtime publication
SELECT schemaname, tablename, pubname
FROM pg_publication_tables 
WHERE tablename IN ('messages', 'profiles');
```

## Bước 3: Test Ứng dụng

1. **Khởi động app:**
   ```bash
   npm run dev
   ```

2. **Đăng ký 2 users test:**
   - Mở 2 tab browser
   - Đăng ký 2 tài khoản khác nhau
   - Kiểm tra profiles đã được tạo tự động

3. **Test chat 1-1:**
   - Tab 1: Chọn user từ danh sách bên trái
   - Gửi tin nhắn
   - Tab 2: Kiểm tra tin nhắn hiển thị realtime

## Cấu trúc Components mới

```
components/
├── UserList.vue         # Danh sách users bên trái
├── ChatBox.vue          # Khung chat chính (đã cập nhật)
├── MessageList.vue      # Danh sách tin nhắn (đã cập nhật)
├── MessageInput.vue     # Input gửi tin nhắn
├── LoginForm.vue        # Form đăng nhập
└── RealtimeDebug.vue    # Debug panel (dev only)
```

## Tính năng mới

### 1. UserList Component
- Hiển thị danh sách tất cả users (trừ chính mình)
- Search users theo tên/email
- Avatar tự động từ initials
- Click để chọn user chat

### 2. Chat 1-1
- Mỗi cặp user có conversation riêng
- Tin nhắn chỉ hiển thị giữa 2 người
- Realtime updates chỉ cho conversation hiện tại

### 3. Auto Profile Creation
- Tự động tạo profile khi user đăng ký
- Sử dụng email làm tên mặc định
- Có thể cập nhật thông tin sau

## Troubleshooting

### Lỗi "relation does not exist"
- Chạy lại script `enable-realtime.sql`
- Kiểm tra bảng đã được tạo

### Không thấy users trong danh sách
- Kiểm tra RLS policies cho bảng profiles
- Đảm bảo user đã đăng nhập
- Kiểm tra trigger tạo profile hoạt động

### Realtime không hoạt động
- Kiểm tra bảng messages và profiles trong realtime publication
- Xem debug panel (chỉ trong dev mode)
- Kiểm tra console logs

### Không gửi được tin nhắn
- Kiểm tra RLS policies cho bảng messages
- Đảm bảo đã chọn user để chat
- Xem console logs để debug

## Demo Flow

1. **Đăng ký/Đăng nhập** → Profile tự động được tạo
2. **Chọn user** từ danh sách bên trái
3. **Gửi tin nhắn** → Hiển thị realtime cho cả 2 users
4. **Chuyển đổi** giữa các conversations khác nhau

## Các file quan trọng

- `enable-realtime.sql` - Script setup database
- `pages/chat.vue` - Trang chat chính
- `components/UserList.vue` - Danh sách users
- `components/ChatBox.vue` - Khung chat
- `REALTIME_TROUBLESHOOTING.md` - Hướng dẫn debug

Sau khi setup xong, bạn sẽ có một hệ thống chat 1-1 hoàn chỉnh! 🎉
