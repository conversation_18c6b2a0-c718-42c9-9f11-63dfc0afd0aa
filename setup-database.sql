-- =============================================
-- CHAT REALTIME DATABASE SETUP
-- =============================================

-- Drop existing tables if they exist (for clean setup)
DROP TABLE IF EXISTS messages CASCADE;
DROP TABLE IF EXISTS profiles CASCADE;

-- =============================================
-- CREATE TABLES
-- =============================================

-- Profiles table for user information
CREATE TABLE profiles (
    id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
    full_name TEXT,
    avatar_url TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL
);

-- Messages table for chat messages
CREATE TABLE messages (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    content TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL
);

-- =============================================
-- CREATE INDEXES
-- =============================================

CREATE INDEX messages_created_at_idx ON messages(created_at);
CREATE INDEX messages_user_id_idx ON messages(user_id);
CREATE INDEX profiles_created_at_idx ON profiles(created_at);

-- =============================================
-- ENABLE ROW LEVEL SECURITY
-- =============================================

ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE messages ENABLE ROW LEVEL SECURITY;

-- =============================================
-- CREATE RLS POLICIES FOR PROFILES
-- =============================================

-- Users can view their own profile
CREATE POLICY "Users can view their own profile"
    ON profiles FOR SELECT
    USING (auth.uid() = id);

-- Users can insert their own profile
CREATE POLICY "Users can insert their own profile"
    ON profiles FOR INSERT
    WITH CHECK (auth.uid() = id);

-- Users can update their own profile
CREATE POLICY "Users can update their own profile"
    ON profiles FOR UPDATE
    USING (auth.uid() = id);

-- =============================================
-- CREATE RLS POLICIES FOR MESSAGES
-- =============================================

-- Anyone authenticated can view all messages (public chat)
CREATE POLICY "Anyone can view messages"
    ON messages FOR SELECT
    TO authenticated
    USING (true);

-- Authenticated users can insert messages with their own user_id
CREATE POLICY "Authenticated users can insert messages"
    ON messages FOR INSERT
    TO authenticated
    WITH CHECK (auth.uid() = user_id);

-- Users can update their own messages
CREATE POLICY "Users can update their own messages"
    ON messages FOR UPDATE
    TO authenticated
    USING (auth.uid() = user_id);

-- Users can delete their own messages
CREATE POLICY "Users can delete their own messages"
    ON messages FOR DELETE
    TO authenticated
    USING (auth.uid() = user_id);

-- =============================================
-- CREATE FUNCTIONS
-- =============================================

-- Function to automatically create profile when user signs up
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.profiles (id, full_name)
    VALUES (
        NEW.id,
        COALESCE(NEW.raw_user_meta_data->>'full_name', split_part(NEW.email, '@', 1))
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to call the function when a new user is created
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- =============================================
-- ENABLE REALTIME
-- =============================================

-- Enable realtime for messages table
ALTER PUBLICATION supabase_realtime ADD TABLE messages;

-- =============================================
-- INSERT SAMPLE DATA (Optional)
-- =============================================

-- Note: You can uncomment the following lines to insert sample data
-- But first you need to create test users through Supabase Auth

/*
-- Sample messages (replace UUIDs with actual user IDs after creating test users)
INSERT INTO messages (user_id, content) VALUES
    ('00000000-0000-0000-0000-000000000001', 'Xin chào mọi người! 👋'),
    ('00000000-0000-0000-0000-000000000002', 'Chào bạn! Bạn có khỏe không?'),
    ('00000000-0000-0000-0000-000000000001', 'Mình khỏe, cảm ơn bạn! Hôm nay thời tiết đẹp quá 😊'),
    ('00000000-0000-0000-0000-000000000002', 'Đúng rồi! Thích hợp để đi dạo 🌞');
*/

-- =============================================
-- VERIFICATION QUERIES
-- =============================================

-- Check if tables were created successfully
SELECT 
    schemaname,
    tablename,
    tableowner
FROM pg_tables 
WHERE schemaname = 'public' 
AND tablename IN ('profiles', 'messages');

-- Check if RLS is enabled
SELECT 
    schemaname,
    tablename,
    rowsecurity
FROM pg_tables 
WHERE schemaname = 'public' 
AND tablename IN ('profiles', 'messages');

-- Check if policies were created
SELECT 
    schemaname,
    tablename,
    policyname,
    cmd,
    roles
FROM pg_policies 
WHERE schemaname = 'public' 
AND tablename IN ('profiles', 'messages');

-- Success message
SELECT 'Database setup completed successfully! 🎉' AS status;
