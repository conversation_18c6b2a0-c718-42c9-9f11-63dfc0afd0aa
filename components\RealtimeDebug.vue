<template>
  <div class="bg-gray-100 p-4 rounded-lg mb-4">
    <h3 class="text-lg font-semibold mb-2">🔧 Realtime Debug</h3>
    
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
      <div class="bg-white p-3 rounded">
        <div class="text-sm font-medium text-gray-600">Connection Status</div>
        <div :class="connectionStatus === 'SUBSCRIBED' ? 'text-green-600' : 'text-red-600'" class="font-bold">
          {{ connectionStatus || 'Not Connected' }}
        </div>
      </div>
      
      <div class="bg-white p-3 rounded">
        <div class="text-sm font-medium text-gray-600">Messages Count</div>
        <div class="text-blue-600 font-bold">{{ messagesCount }}</div>
      </div>
      
      <div class="bg-white p-3 rounded">
        <div class="text-sm font-medium text-gray-600">Realtime Events</div>
        <div class="text-purple-600 font-bold">{{ realtimeEvents }}</div>
      </div>
    </div>

    <div class="flex gap-2 mb-4">
      <UButton @click="testConnection" size="sm" variant="outline">
        Test Connection
      </UButton>
      <UButton @click="sendTestMessage" size="sm" variant="outline" :loading="sending">
        Send Test Message
      </UButton>
      <UButton @click="clearLogs" size="sm" variant="outline" color="red">
        Clear Logs
      </UButton>
    </div>

    <div class="bg-black text-green-400 p-3 rounded text-xs font-mono max-h-40 overflow-y-auto">
      <div v-for="(log, index) in logs" :key="index" class="mb-1">
        <span class="text-gray-400">[{{ log.time }}]</span> {{ log.message }}
      </div>
      <div v-if="logs.length === 0" class="text-gray-500">
        No logs yet...
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  messagesCount: number
}

const props = defineProps<Props>()

const supabase = useSupabaseClient()
const user = useSupabaseUser()
const toast = useToast()

const connectionStatus = ref('')
const realtimeEvents = ref(0)
const logs = ref<Array<{time: string, message: string}>>([])
const sending = ref(false)

let debugChannel: any

const addLog = (message: string) => {
  logs.value.push({
    time: new Date().toLocaleTimeString(),
    message
  })
  
  // Keep only last 20 logs
  if (logs.value.length > 20) {
    logs.value = logs.value.slice(-20)
  }
}

const testConnection = async () => {
  addLog('🔍 Testing Supabase connection...')
  
  try {
    const { data, error } = await supabase
      .from('messages')
      .select('count(*)')
      .limit(1)
    
    if (error) {
      addLog(`❌ Connection error: ${error.message}`)
    } else {
      addLog('✅ Database connection successful')
    }
  } catch (err) {
    addLog(`❌ Connection failed: ${err.message}`)
  }
}

const sendTestMessage = async () => {
  if (!user.value) {
    addLog('❌ No user logged in')
    return
  }
  
  sending.value = true
  addLog('📤 Sending test message...')
  
  try {
    const { data, error } = await supabase
      .from('messages')
      .insert({
        user_id: user.value.id,
        content: `Test message at ${new Date().toLocaleTimeString()}`
      })
      .select()
    
    if (error) {
      addLog(`❌ Send error: ${error.message}`)
    } else {
      addLog('✅ Test message sent successfully')
    }
  } catch (err) {
    addLog(`❌ Send failed: ${err.message}`)
  } finally {
    sending.value = false
  }
}

const clearLogs = () => {
  logs.value = []
}

const setupDebugChannel = () => {
  addLog('🔧 Setting up debug realtime channel...')
  
  debugChannel = supabase
    .channel('debug-messages')
    .on(
      'postgres_changes',
      {
        event: 'INSERT',
        schema: 'public',
        table: 'messages'
      },
      (payload) => {
        realtimeEvents.value++
        addLog(`📨 Realtime event received: ${payload.new.content}`)
      }
    )
    .subscribe((status) => {
      connectionStatus.value = status
      addLog(`🔗 Subscription status: ${status}`)
    })
}

onMounted(() => {
  setupDebugChannel()
  addLog('🚀 Debug component mounted')
})

onUnmounted(() => {
  if (debugChannel) {
    supabase.removeChannel(debugChannel)
    addLog('🔌 Debug channel disconnected')
  }
})
</script>
