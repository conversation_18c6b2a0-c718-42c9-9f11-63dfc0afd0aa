-- =============================================
-- SIMPLE CHAT SCHEMA - MINIMAL SETUP
-- =============================================

-- Create messages table
CREATE TABLE IF NOT EXISTS messages (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    content TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL
);

-- Create index for better performance
CREATE INDEX IF NOT EXISTS messages_created_at_idx ON messages(created_at);
CREATE INDEX IF NOT EXISTS messages_user_id_idx ON messages(user_id);

-- Enable Row Level Security
ALTER TABLE messages ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Anyone can view messages" ON messages;
DROP POLICY IF EXISTS "Authenticated users can insert messages" ON messages;

-- Create RLS policies
-- Anyone authenticated can view all messages (public chat)
CREATE POLICY "Anyone can view messages"
    ON messages FOR SELECT
    TO authenticated
    USING (true);

-- Authenticated users can insert messages with their own user_id
CREATE POLICY "Authenticated users can insert messages"
    ON messages FOR INSERT
    TO authenticated
    WITH CHECK (auth.uid() = user_id);

-- Enable realtime for messages table
ALTER PUBLICATION supabase_realtime ADD TABLE messages;

-- Test query to verify setup
SELECT 'Messages table created successfully! 🎉' AS status;

-- Show table structure
\d messages;
