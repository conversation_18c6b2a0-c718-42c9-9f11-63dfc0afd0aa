<template>
  <div class="h-full flex flex-col">
    <!-- Loading state -->
    <div v-if="loading" class="flex-1 flex items-center justify-center">
      <div class="text-center">
        <Icon
          name="heroicons:arrow-path"
          class="w-8 h-8 text-gray-400 animate-spin mx-auto mb-2"
        />
        <p class="text-gray-500"><PERSON><PERSON> tải tin nhắn...</p>
      </div>
    </div>

    <!-- Empty state -->
    <div
      v-else-if="messages.length === 0"
      class="flex-1 flex items-center justify-center"
    >
      <div class="text-center">
        <Icon
          name="heroicons:chat-bubble-left-right"
          class="w-12 h-12 text-gray-300 mx-auto mb-4"
        />
        <h3 class="text-lg font-medium text-gray-900 mb-2">
          Chưa có tin nhắn nào
        </h3>
        <p class="text-gray-500"><PERSON><PERSON><PERSON> b<PERSON><PERSON> đ<PERSON>u cuộc tr<PERSON> chuyện!</p>
      </div>
    </div>

    <!-- Messages -->
    <div
      v-else
      ref="messagesContainer"
      class="flex-1 overflow-y-auto p-4 space-y-4 scroll-smooth"
    >
      <div
        v-for="message in messages"
        :key="message.id"
        :class="[
          'flex',
          message.user_id === user?.id ? 'justify-end' : 'justify-start',
        ]"
      >
        <div
          :class="[
            'max-w-xs lg:max-w-md px-4 py-2 rounded-lg',
            message.user_id === user?.id
              ? 'bg-primary-500 text-white'
              : 'bg-white text-gray-900 shadow-sm border border-gray-200',
          ]"
        >
          <!-- Message content -->
          <p class="text-sm">{{ message.content }}</p>

          <!-- Message metadata -->
          <div
            :class="[
              'flex items-center justify-between mt-1 text-xs',
              message.user_id === user?.id
                ? 'text-primary-100'
                : 'text-gray-500',
            ]"
          >
            <span>
              {{
                message.user_id === user?.id
                  ? "Bạn"
                  : message.profiles?.full_name || "Ẩn danh"
              }}
            </span>
            <span>{{ formatTime(message.created_at) }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { format } from "date-fns";
import { vi } from "date-fns/locale";

interface Message {
  id: string;
  user_id: string;
  content: string;
  created_at: string;
  profiles?: {
    full_name?: string;
  };
}

interface Props {
  messages: Message[];
  loading: boolean;
}

const props = defineProps<Props>();
const user = useSupabaseUser();
const messagesContainer = ref<HTMLElement>();

// Format time
const formatTime = (dateString: string) => {
  const date = new Date(dateString);
  const now = new Date();
  const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

  if (diffInHours < 24) {
    return format(date, "HH:mm", { locale: vi });
  } else {
    return format(date, "dd/MM HH:mm", { locale: vi });
  }
};

// Auto scroll to bottom when new messages arrive
watch(
  () => props.messages.length,
  () => {
    nextTick(() => {
      if (messagesContainer.value) {
        messagesContainer.value.scrollTop =
          messagesContainer.value.scrollHeight;
      }
    });
  }
);

// Scroll to bottom on mount
onMounted(() => {
  nextTick(() => {
    if (messagesContainer.value) {
      messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight;
    }
  });
});
</script>
