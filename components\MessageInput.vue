<template>
  <form @submit.prevent="handleSubmit" class="flex items-end space-x-3">
    <!-- Message input -->
    <div class="flex-1">
      <UTextarea
        v-model="message"
        placeholder="Nhập tin nhắn..."
        :rows="1"
        :maxrows="4"
        :disabled="sending"
        @keydown.enter.exact.prevent="handleSubmit"
        @keydown.enter.shift.exact="handleNewLine"
        class="resize-none"
        autoresize
      />
      <p class="text-xs text-gray-500 mt-1">
        Nhấn Enter để gử<PERSON>, Shift + Enter để xuống dòng
      </p>
    </div>

    <!-- Send button -->
    <UButton
      type="submit"
      :disabled="!message.trim() || sending"
      :loading="sending"
      icon="heroicons:paper-airplane"
      size="lg"
      class="shrink-0"
    >
      Gửi
    </UButton>
  </form>
</template>

<script setup lang="ts">
interface Emits {
  (e: 'send-message', message: string): void
}

interface Props {
  sending: boolean
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const message = ref('')

const handleSubmit = () => {
  if (!message.value.trim() || props.sending) return

  emit('send-message', message.value)
  message.value = ''
}

const handleNewLine = () => {
  message.value += '\n'
}
</script>
