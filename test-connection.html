<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Supabase Connection</title>
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        input {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .form-group {
            margin: 15px 0;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Test Supabase Connection</h1>
        
        <div class="form-group">
            <label for="supabaseUrl">Supabase URL:</label>
            <input type="text" id="supabaseUrl" placeholder="https://your-project.supabase.co">
        </div>
        
        <div class="form-group">
            <label for="supabaseKey">Supabase Anon Key:</label>
            <input type="text" id="supabaseKey" placeholder="your-anon-key">
        </div>
        
        <button onclick="testConnection()">Test Connection</button>
        <button onclick="testAuth()">Test Auth</button>
        <button onclick="testMessages()">Test Messages Table</button>
        
        <div id="results"></div>
    </div>

    <script>
        let supabase;
        
        function initSupabase() {
            const url = document.getElementById('supabaseUrl').value;
            const key = document.getElementById('supabaseKey').value;
            
            if (!url || !key) {
                showResult('error', 'Vui lòng nhập URL và Key');
                return false;
            }
            
            supabase = window.supabase.createClient(url, key);
            return true;
        }
        
        function showResult(type, message) {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `status ${type}`;
            div.innerHTML = `<strong>${new Date().toLocaleTimeString()}:</strong> ${message}`;
            results.appendChild(div);
            results.scrollTop = results.scrollHeight;
        }
        
        async function testConnection() {
            if (!initSupabase()) return;
            
            showResult('info', 'Đang test kết nối...');
            
            try {
                const { data, error } = await supabase
                    .from('messages')
                    .select('count(*)')
                    .limit(1);
                
                if (error) {
                    showResult('error', `Lỗi kết nối: ${error.message}`);
                } else {
                    showResult('success', '✅ Kết nối thành công! Bảng messages tồn tại.');
                }
            } catch (err) {
                showResult('error', `Lỗi: ${err.message}`);
            }
        }
        
        async function testAuth() {
            if (!initSupabase()) return;
            
            showResult('info', 'Đang test Auth service...');
            
            try {
                const { data, error } = await supabase.auth.getSession();
                
                if (error) {
                    showResult('error', `Lỗi Auth: ${error.message}`);
                } else {
                    showResult('success', '✅ Auth service hoạt động bình thường.');
                    if (data.session) {
                        showResult('info', `User đã đăng nhập: ${data.session.user.email}`);
                    } else {
                        showResult('info', 'Chưa có user nào đăng nhập.');
                    }
                }
            } catch (err) {
                showResult('error', `Lỗi: ${err.message}`);
            }
        }
        
        async function testMessages() {
            if (!initSupabase()) return;
            
            showResult('info', 'Đang test bảng messages...');
            
            try {
                // Test select
                const { data, error } = await supabase
                    .from('messages')
                    .select('*')
                    .limit(5);
                
                if (error) {
                    showResult('error', `Lỗi truy vấn: ${error.message}`);
                } else {
                    showResult('success', `✅ Truy vấn thành công! Có ${data.length} tin nhắn.`);
                    if (data.length > 0) {
                        showResult('info', `Tin nhắn mới nhất: "${data[data.length-1].content}"`);
                    }
                }
            } catch (err) {
                showResult('error', `Lỗi: ${err.message}`);
            }
        }
        
        // Auto-fill from localStorage if available
        window.onload = function() {
            const savedUrl = localStorage.getItem('supabaseUrl');
            const savedKey = localStorage.getItem('supabaseKey');
            
            if (savedUrl) document.getElementById('supabaseUrl').value = savedUrl;
            if (savedKey) document.getElementById('supabaseKey').value = savedKey;
            
            // Save values when changed
            document.getElementById('supabaseUrl').onchange = function() {
                localStorage.setItem('supabaseUrl', this.value);
            };
            document.getElementById('supabaseKey').onchange = function() {
                localStorage.setItem('supabaseKey', this.value);
            };
        };
    </script>
</body>
</html>
