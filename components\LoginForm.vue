<template>
  <form @submit.prevent="handleSubmit" class="mt-8 space-y-6">
    <div class="rounded-md shadow-sm -space-y-px">
      <div>
        <label for="email" class="sr-only">Email</label>
        <UInput
          id="email"
          v-model="form.email"
          type="email"
          placeholder="Email"
          required
          :disabled="loading"
          class="relative block w-full rounded-t-md"
        />
      </div>
      <div>
        <label for="password" class="sr-only">Mật khẩu</label>
        <UInput
          id="password"
          v-model="form.password"
          type="password"
          placeholder="Mật khẩu"
          required
          :disabled="loading"
          class="relative block w-full rounded-b-md"
        />
      </div>
    </div>

    <div v-if="error" class="text-red-600 text-sm text-center">
      {{ error }}
    </div>

    <div>
      <UButton
        type="submit"
        :loading="loading"
        :disabled="loading"
        class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
      >
        {{ isRegister ? "Đăng ký" : "Đăng nhập" }}
      </UButton>
    </div>
  </form>
</template>

<script setup lang="ts">
interface Props {
  isRegister: boolean;
}

interface Emits {
  (e: "toggle-mode"): void;
}

const props = defineProps<Props>();

const supabase = useSupabaseClient();
const toast = useToast();

const form = reactive({
  email: "",
  password: "",
});

const loading = ref(false);
const error = ref("");

const handleSubmit = async () => {
  loading.value = true;
  error.value = "";

  try {
    if (props.isRegister) {
      const { error: signUpError } = await supabase.auth.signUp({
        email: form.email,
        password: form.password,
      });

      if (signUpError) {
        error.value = signUpError.message;
      } else {
        toast.add({
          title: "Đăng ký thành công!",
          description: "Vui lòng kiểm tra email để xác nhận tài khoản.",
          color: "green",
        });
      }
    } else {
      const { error: signInError } = await supabase.auth.signInWithPassword({
        email: form.email,
        password: form.password,
      });

      if (signInError) {
        error.value = signInError.message;
      } else {
        toast.add({
          title: "Đăng nhập thành công!",
          color: "green",
        });
        await navigateTo("/chat");
      }
    }
  } catch (err) {
    error.value = "Có lỗi xảy ra. Vui lòng thử lại.";
  } finally {
    loading.value = false;
  }
};
</script>
