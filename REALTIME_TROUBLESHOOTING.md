# 🔧 Realtime Troubleshooting Guide

## Vấn đề: Không thấy tin nhắn realtime

### 1. Ki<PERSON>m tra Database Setup

**Bước 1: Chạy script enable realtime**
```sql
-- Copy và chạy nội dung file enable-realtime.sql trong Supabase SQL Editor
```

**Bước 2: Kiể<PERSON> tra bảng messages có trong realtime publication**
```sql
SELECT 
    schemaname,
    tablename,
    pubname
FROM pg_publication_tables 
WHERE tablename = 'messages';
```

Kết quả mong đợi:
```
schemaname | tablename | pubname
-----------+-----------+------------------
public     | messages  | supabase_realtime
```

### 2. Kiểm tra RLS Policies

```sql
SELECT 
    schemaname,
    tablename,
    policyname,
    cmd
FROM pg_policies 
WHERE tablename = 'messages';
```

<PERSON><PERSON><PERSON> có ít nhất 2 policies:
- `Anyone can view messages` (SELECT)
- `Authenticated users can insert messages` (INSERT)

### 3. <PERSON><PERSON><PERSON> tra trong Supabase Dashboard

**Bước 1: Vào Database > Replication**
- <PERSON><PERSON>m bảo bảng `messages` có trong danh sách
- Nếu không có, nhấn "Add table" và chọn `messages`

**Bước 2: Vào Database > Tables**
- Mở bảng `messages`
- Kiểm tra có dữ liệu không
- Thử insert thủ công 1 record

### 4. Debug trong Browser

**Bước 1: Mở Developer Tools (F12)**
- Vào tab Console
- Tìm các log từ ứng dụng

**Bước 2: Kiểm tra các log sau:**
```
✅ "Setting up realtime subscription..."
✅ "Realtime subscription status: SUBSCRIBED"
✅ "Successfully subscribed to realtime messages"
```

**Bước 3: Test gửi tin nhắn**
- Gửi tin nhắn từ ứng dụng
- Kiểm tra log:
```
✅ "Sending message: ..."
✅ "Insert result: ..."
✅ "Realtime message received: ..."
```

### 5. Sử dụng Debug Component

Ứng dụng có component debug tích hợp (chỉ hiện trong development):

1. **Connection Status**: Phải là "SUBSCRIBED"
2. **Test Connection**: Nhấn để test database
3. **Send Test Message**: Nhấn để gửi tin nhắn test
4. **Logs**: Xem chi tiết các event

### 6. Các lỗi thường gặp

#### Lỗi: "relation does not exist"
**Nguyên nhân**: Bảng messages chưa được tạo
**Giải pháp**: Chạy lại `simple-chat-schema.sql`

#### Lỗi: "permission denied"
**Nguyên nhân**: RLS policies chưa đúng hoặc user chưa đăng nhập
**Giải pháp**: 
- Kiểm tra user đã đăng nhập
- Kiểm tra RLS policies

#### Lỗi: "CHANNEL_ERROR"
**Nguyên nhân**: Realtime chưa được enable
**Giải pháp**: 
- Chạy `enable-realtime.sql`
- Kiểm tra trong Dashboard > Database > Replication

#### Realtime không hoạt động
**Nguyên nhân**: Có thể do network hoặc cấu hình
**Giải pháp**:
1. Refresh trang
2. Kiểm tra network connection
3. Kiểm tra Supabase project status
4. Thử tạo channel mới với tên khác

### 7. Test Manual

**Cách 1: Test trong SQL Editor**
```sql
-- Insert tin nhắn thủ công
INSERT INTO messages (user_id, content) 
VALUES ('your-user-id', 'Test message from SQL');
```

**Cách 2: Test với 2 tab browser**
- Mở 2 tab với ứng dụng
- Đăng nhập cùng 1 user hoặc 2 user khác nhau
- Gửi tin nhắn từ tab 1
- Kiểm tra tab 2 có nhận được không

### 8. Kiểm tra Network

**Trong Developer Tools > Network tab:**
- Tìm các request đến Supabase
- Kiểm tra có lỗi 4xx, 5xx không
- Kiểm tra WebSocket connections

### 9. Restart và Clear Cache

1. **Hard refresh**: Ctrl + Shift + R
2. **Clear browser cache**
3. **Restart development server**: 
   ```bash
   # Stop server (Ctrl + C)
   npm run dev
   ```

### 10. Liên hệ Support

Nếu vẫn không hoạt động, cung cấp thông tin sau:
- Console logs (F12 > Console)
- Network errors (F12 > Network)
- Supabase project URL
- Kết quả các query kiểm tra ở trên

## ✅ Checklist Troubleshooting

- [ ] Database schema đã được tạo
- [ ] Bảng messages có trong realtime publication  
- [ ] RLS policies đã được tạo đúng
- [ ] User đã đăng nhập thành công
- [ ] Console log hiển thị "SUBSCRIBED"
- [ ] Có thể insert tin nhắn thành công
- [ ] Debug component hiển thị connection OK
- [ ] Test với 2 tab browser
- [ ] Network không có lỗi
- [ ] Đã thử restart và clear cache
