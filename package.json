{"name": "nuxt-app", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare", "seed": "node seed.mjs"}, "dependencies": {"@nuxt/ui": "^2.21.0", "nuxt": "^3.15.2", "vue": "latest", "vue-router": "latest"}, "devDependencies": {"@faker-js/faker": "^9.4.0", "@nuxtjs/supabase": "^1.4.6", "date-fns": "^4.1.0", "zod": "^3.24.1"}}