# Hướng dẫn Setup Database cho Chat App

## Bước 1: <PERSON><PERSON><PERSON> cập Supabase Dashboard

1. <PERSON><PERSON><PERSON> nhập vào [Supabase Dashboard](https://supabase.com/dashboard)
2. Chọn project của bạn
3. V<PERSON><PERSON> mục **SQL Editor** ở sidebar bên trái

## Bước 2: Ch<PERSON>y Schema

### Cách 1: Sử dụng file simple-chat-schema.sql (Khuyến nghị)

1. Mở file `simple-chat-schema.sql` 
2. Copy toàn bộ nội dung
3. Paste vào SQL Editor trong Supabase Dashboard
4. Nhấn **Run** để thực thi

### Cách 2: Ch<PERSON>y từng lệnh thủ công

Paste và chạy từng đoạn SQL sau:

```sql
-- Tạo bảng messages
CREATE TABLE IF NOT EXISTS messages (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    content TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL
);
```

```sql
-- Tạo indexes
CREATE INDEX IF NOT EXISTS messages_created_at_idx ON messages(created_at);
CREATE INDEX IF NOT EXISTS messages_user_id_idx ON messages(user_id);
```

```sql
-- Bật Row Level Security
ALTER TABLE messages ENABLE ROW LEVEL SECURITY;
```

```sql
-- Tạo RLS policies
CREATE POLICY "Anyone can view messages"
    ON messages FOR SELECT
    TO authenticated
    USING (true);

CREATE POLICY "Authenticated users can insert messages"
    ON messages FOR INSERT
    TO authenticated
    WITH CHECK (auth.uid() = user_id);
```

```sql
-- Bật Realtime
ALTER PUBLICATION supabase_realtime ADD TABLE messages;
```

## Bước 3: Kiểm tra Setup

Chạy query sau để kiểm tra:

```sql
-- Kiểm tra bảng đã tạo
SELECT table_name, column_name, data_type 
FROM information_schema.columns 
WHERE table_name = 'messages';

-- Kiểm tra RLS policies
SELECT policyname, cmd, roles 
FROM pg_policies 
WHERE tablename = 'messages';
```

## Bước 4: Test Authentication

1. Vào mục **Authentication** > **Users**
2. Tạo một user test hoặc sử dụng email/password signup trong app
3. Kiểm tra user đã được tạo thành công

## Bước 5: Test Realtime

1. Vào mục **Database** > **Replication**
2. Đảm bảo bảng `messages` đã được thêm vào publication `supabase_realtime`
3. Nếu chưa có, nhấn **Add table** và chọn `messages`

## Troubleshooting

### Lỗi "relation does not exist"
- Đảm bảo đã chạy đúng schema
- Kiểm tra tên bảng có đúng không

### Lỗi "permission denied"
- Kiểm tra RLS policies đã được tạo
- Đảm bảo user đã đăng nhập

### Realtime không hoạt động
- Kiểm tra bảng messages đã được thêm vào publication
- Restart ứng dụng sau khi thay đổi realtime settings

## Cấu trúc Database

```
messages
├── id (UUID, Primary Key)
├── user_id (UUID, Foreign Key -> auth.users.id)
├── content (TEXT)
└── created_at (TIMESTAMP WITH TIME ZONE)
```

## RLS Policies

1. **Anyone can view messages**: Tất cả user đã xác thực có thể đọc tin nhắn
2. **Authenticated users can insert messages**: User chỉ có thể gửi tin nhắn với user_id của chính mình

Sau khi hoàn thành setup, ứng dụng chat sẽ hoạt động bình thường!
